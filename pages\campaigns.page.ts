import { Page } from '@playwright/test';
import { Campaign_objects } from "../test-objects/campaign_objects";
import { captureAriaSnapshot } from '../lib/test-logger';
import { compareAriaSnapshot, validateModalVisibility } from '../lib/aria-snapshot-utils';

// Type for accessibility snapshot node
interface AccessibilityNode {
    role?: string;
    name?: string;
    children?: AccessibilityNode[];
}

export class CampaignsPage {
    private page: Page;
    private campaignObjects: Campaign_objects;

    constructor(page: Page) {
        this.page = page;
        this.campaignObjects = new Campaign_objects(page);
    }

    async navigateToCampaigns() {
        await this.campaignObjects.campaignLocator();
    }

    async selectRandomCampaign() {
        await this.campaignObjects.randomCampaignSelection();
    }

    async clickNewLeadButton() {
        await this.page.click('button#c14');
        await this.page.waitForTimeout(4000);
    }
    

    async isNewLeadModalVisible() {
        try {
            // Use real baseline snapshot comparison for lead details container
            const comparison = await compareAriaSnapshot(this.page, 'lead-details', {
                selector: '#lead-details',
                category: 'campaigns'
            });

            // If snapshots match closely, consider lead details visible
            if (comparison.matches) {
                return true;
            }

            // If there are only minor differences, still consider visible
            const minorDifferences = comparison.differences.length <= 5;
            if (minorDifferences) {
                console.log('Lead details visible with minor differences:', comparison.differences);
                return true;
            }

            return false;
        } catch (error) {
            // Fallback to original method if snapshot comparison fails
            console.warn('Snapshot comparison failed, using fallback method:', error);

            // Check if #lead-details element exists and is visible
            try {
                const leadDetailsElement = await this.page.$('#lead-details');
                return leadDetailsElement ? await leadDetailsElement.isVisible() : false;
            } catch (fallbackError) {
                console.warn('Fallback method also failed:', fallbackError);
                return false;
            }
        }
    }

    async clickNewCampaignButton() {
        await this.page.click('//*[@id="c1"]');
        await this.page.waitForTimeout(2000);
    }

    async isNewCampaignModalVisible() {
        try {
            // Use real baseline snapshot comparison
            return await validateModalVisibility(
                this.page,
                'campaign-details-modal',
                'dialog',
                'Campaign Details'
            );
        } catch (error) {
            // Fallback to original method if snapshot comparison fails
            console.warn('Snapshot comparison failed, using fallback method:', error);
            const snapshot = await captureAriaSnapshot(this.page, {
                contextSelector: '#CampaignDetailsModalLabel',
                interestingOnly: true
            });

            // Verify the modal has correct ARIA attributes
            const modalNode = (snapshot?.children || []).find((node: AccessibilityNode) =>
                node.role === 'dialog' &&
                node.name === 'Campaign Details'
            );

            return !!modalNode;
        }
    }

    async clickCaptureSessions() {
        const linkCaptureSessions = this.page.getByRole("link", {name: "Capture Sessions"});
        await linkCaptureSessions.click();
        await this.page.waitForTimeout(1000);
    }

    async clickNewSession() {
        const buttonNewSession = this.page.getByRole("button", {name: " New Session"});
        await buttonNewSession.click();
        await this.page.waitForTimeout(1000);
    }

    async isNewSessionModalVisible() {
        try {
            // Use real baseline snapshot comparison
            return await validateModalVisibility(
                this.page,
                'lead-capture-session-modal',
                'dialog',
                'Lead Capture Session'
            );
        } catch (error) {
            // Fallback to original method if snapshot comparison fails
            console.warn('Snapshot comparison failed, using fallback method:', error);
            const snapshot = await captureAriaSnapshot(this.page, {
                contextSelector: '#LeadCaptureSessionModalLabel',
                interestingOnly: true
            });

            // Verify the modal has correct ARIA attributes
            const modalNode = (snapshot?.children || []).find((node: AccessibilityNode) =>
                node.role === 'dialog' &&
                node.name === 'Lead Capture Session'
            );

            return !!modalNode;
        }
    }

    async clickImportLeadsButton() {
        await this.page.click('//div[@id="crm_leads"]/div/div[2]/div[@class="row"]/div[2]/span/button[@name="c15"]');
        await this.page.waitForTimeout(1000);
    }

    async selectLeadGenerator() {
        await this.page.click('//form[@id="CampaignCategory_Detail"]/span[1]/div/div[@role="dialog"]//ul/li[1]');
        await this.page.waitForTimeout(2000);
    }

    async selectLeadManager() {
        await this.page.click('//form[@id="CampaignCategory_Detail"]/span[2]/div/div[@role="dialog"]//ul/li[1]');
        await this.page.waitForTimeout(2000);
    }

    async isLeadUploadModalVisible() {
        const fileUploadModal = await this.page.$('//div[@id="importModal"]/div[@class="modal-dialog modal-lg"]//div[@class="modal-header"]');
        return fileUploadModal ? fileUploadModal.isVisible() : false;
    }

    async areCampaignTilesVisible() {
        try {
            // Use real baseline snapshot comparison
            const comparison = await compareAriaSnapshot(this.page, 'campaign-tiles', {
                selector: 'body',  // Use body since that's what was captured
                category: 'campaigns'
            });

            // If snapshots match closely, consider tiles visible
            if (comparison.matches) {
                return true;
            }

            // If there are only minor differences, still consider visible
            const minorDifferences = comparison.differences.length <= 10;
            if (minorDifferences) {
                console.log('Campaign tiles visible with minor differences:', comparison.differences);
                return true;
            }

            return false;
        } catch (error) {
            // Fallback to original method if snapshot comparison fails
            console.warn('Snapshot comparison failed, using fallback method:', error);

            // Check for actual campaign tiles based on campaign_objects.ts
            try {
                const campaignTiles = await this.page.$$('.panel.panel-default.tile.tile-hover');
                return campaignTiles.length > 0;
            } catch (fallbackError) {
                console.warn('Fallback method also failed:', fallbackError);
                return false;
            }
        }
    }
}
