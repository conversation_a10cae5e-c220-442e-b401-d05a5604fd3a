{"timestamp": "2025-07-14T20:16:41.949Z", "url": "https://members.inscape.co.za/App/InscapeAdmin/Individual_Overview/", "snapshot": {"role": "WebArea", "name": "Connect - Members - Individual Overview", "children": [{"role": "link", "name": " Admin"}, {"role": "link", "name": " Calendar"}, {"role": "text", "name": ""}, {"role": "link", "name": "Individual"}, {"role": "text", "name": ""}, {"role": "text", "name": "CRM"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Communication"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Enrolments"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Finances"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Academics"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "link", "name": "Facilities"}, {"role": "text", "name": ""}, {"role": "text", "name": "Classroom Management"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "link", "name": "Data Analytics"}, {"role": "text", "name": ""}, {"role": "link", "name": "Graduation"}, {"role": "text", "name": ""}, {"role": "link", "name": "Compliance"}, {"role": "text", "name": ""}, {"role": "link", "name": "Policies And Procedures"}, {"role": "text", "name": ""}, {"role": "text", "name": "System Configuration"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Test Bot"}, {"role": "text", "name": ""}, {"role": "heading", "name": "INDIVIDUALS", "level": 3}, {"role": "tab", "name": "Students"}, {"role": "tab", "name": "Applicants"}, {"role": "tab", "name": "Staff"}, {"role": "tab", "name": "Account Payers"}, {"role": "tab", "name": "Other"}, {"role": "tab", "name": "Employment Details"}, {"role": "text", "name": "Role"}, {"role": "combobox", "name": "Role", "haspopup": "menu", "children": [{"role": "option", "name": "All", "selected": true}, {"role": "option", "name": "Not Assigned"}, {"role": "option", "name": "InscapeAdmin"}, {"role": "option", "name": "Tutor"}, {"role": "option", "name": "Student"}, {"role": "option", "name": "SalesAgent"}, {"role": "option", "name": "CampusOffice"}, {"role": "option", "name": "CampusAcademics"}, {"role": "option", "name": "Account<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "InscapeMember"}], "value": "All"}, {"role": "text", "name": "Status"}, {"role": "combobox", "name": "Status", "haspopup": "menu", "children": [{"role": "option", "name": "All", "selected": true}, {"role": "option", "name": "Active"}, {"role": "option", "name": "Ineligible"}, {"role": "option", "name": "Blocked"}], "value": "All"}, {"role": "text", "name": "Tag"}, {"role": "combobox", "name": "Tag", "haspopup": "menu", "children": [{"role": "option", "name": "None", "selected": true}, {"role": "option", "name": "Academic"}, {"role": "option", "name": "Advanced Certificate"}, {"role": "option", "name": "Advanced Diploma"}, {"role": "option", "name": "After 5"}, {"role": "option", "name": "After 5"}, {"role": "option", "name": "Application Manager"}, {"role": "option", "name": "Apprenticeship/Trade Cert"}, {"role": "option", "name": "Archive Enrolment"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "BA Honours - Distance"}, {"role": "option", "name": "Bachelor honours degree"}, {"role": "option", "name": "Bachelor's degree (Level 7)"}, {"role": "option", "name": "Bachelor's degree (Level 8)"}, {"role": "option", "name": "Bad Debt Student FY22"}, {"role": "option", "name": "Bad Debt Student FY23"}, {"role": "option", "name": "Belgotex and The Go Group 2021 Ecosystems for Change Scholarship"}, {"role": "option", "name": "Belgotex Scholarship Student - 2020"}, {"role": "option", "name": "Belgotex Scholarship Student - 2021"}, {"role": "option", "name": "Belgotex Scholarship Student - 2022"}, {"role": "option", "name": "Belgotex Scholarship Student - 2023"}, {"role": "option", "name": "Blended Learning - Architectural Technology - July"}, {"role": "option", "name": "BUSY"}, {"role": "option", "name": "CALL ME LATER"}, {"role": "option", "name": "Campus Director"}, {"role": "option", "name": "Campus Director Assistant"}, {"role": "option", "name": "Campus Office"}, {"role": "option", "name": "Cape Town Campus"}, {"role": "option", "name": "Career Day and Expo"}, {"role": "option", "name": "CAT (Credit Accumulation Transfer)"}, {"role": "option", "name": "CFC Academic"}, {"role": "option", "name": "CFC Attendance"}, {"role": "option", "name": "CFC Conduct"}, {"role": "option", "name": "CFC Finance"}, {"role": "option", "name": "CFC Non-Submission"}, {"role": "option", "name": "CFC Plagiarism"}, {"role": "option", "name": "Content Creator"}, {"role": "option", "name": "Crafts/Trades"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "Dean Representative"}, {"role": "option", "name": "Diploma (Min 240)"}, {"role": "option", "name": "Diploma (Min 360)"}, {"role": "option", "name": "Direct Leads"}, {"role": "option", "name": "Distance Campus"}, {"role": "option", "name": "Doctoral Degree"}, {"role": "option", "name": "Dubai Online Campus"}, {"role": "option", "name": "Dubai Virtual Campus"}, {"role": "option", "name": "Durban Campus"}, {"role": "option", "name": "Enrolment POP"}, {"role": "option", "name": "Events"}, {"role": "option", "name": "Executive/Administration/Management professional"}, {"role": "option", "name": "External Moderator"}, {"role": "option", "name": "External Re-mark Request 2023"}, {"role": "option", "name": "Foundation Learning Cert"}, {"role": "option", "name": "Further Diploma"}, {"role": "option", "name": "Further Education and Training Certificate"}, {"role": "option", "name": "Head Office"}, {"role": "option", "name": "Higher Certificate"}, {"role": "option", "name": "HLB Trust Scholarship Student - 2015"}, {"role": "option", "name": "HLB Trust Scholarship Student - 2016"}, {"role": "option", "name": "HLB Trust Scholarship Student - 2017"}, {"role": "option", "name": "HLB Trust Scholarship Student - 2018"}, {"role": "option", "name": "HLB Trust Scholarship Student - 2020"}, {"role": "option", "name": "HLB Trust Scholarship Student - 2021"}, {"role": "option", "name": "HLB Trust Scholarship Student - 2022"}, {"role": "option", "name": "HLB Trust Scholarship Student - 2023"}, {"role": "option", "name": "HLB Trust Scholarship Student - 2024"}, {"role": "option", "name": "HLB Trust Scholarship Student - 2025"}, {"role": "option", "name": "HLB Trust Scholarship Student -2019"}, {"role": "option", "name": "<PERSON><PERSON>"}, {"role": "option", "name": "HOT"}, {"role": "option", "name": "IEG & The Change Collective Africa (Pty) 2025"}, {"role": "option", "name": "IEG Financial Assistance Student - 2015"}, {"role": "option", "name": "IEG Financial Assistance Student - 2016"}, {"role": "option", "name": "IEG Financial Assistance Student - 2017"}, {"role": "option", "name": "IEG Financial Assistance Student - 2018"}, {"role": "option", "name": "IEG Financial Assistance Student - 2019"}, {"role": "option", "name": "IEG Financial Assistance Student - 2020"}, {"role": "option", "name": "IEG Financial Assistance Student - 2021"}, {"role": "option", "name": "IEG Financial Assistance Student - 2022"}, {"role": "option", "name": "IEG Financial Assistance Student - 2024"}, {"role": "option", "name": "IEG Financial Assistance Student - 2025"}, {"role": "option", "name": "Inscape Exchange 2019"}, {"role": "option", "name": "Instructional/Research professional"}, {"role": "option", "name": "Internal Moderator"}, {"role": "option", "name": "Internal Re-mark Request 2023"}, {"role": "option", "name": "Interviewer"}, {"role": "option", "name": "Invoice Past Fin Year Editor"}, {"role": "option", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"role": "option", "name": "L1 Certificate FD"}, {"role": "option", "name": "Late Enrolment 2024"}, {"role": "option", "name": "Late Enrolment 2025"}, {"role": "option", "name": "Lead Manager - Contact"}, {"role": "option", "name": "Lead Manager - <PERSON><PERSON>"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "Masters Degree"}, {"role": "option", "name": "Midrand Campus"}, {"role": "option", "name": "Mimecast Test Tag"}, {"role": "option", "name": "National Certificate"}, {"role": "option", "name": "National Diploma"}, {"role": "option", "name": "National Higher Certificate"}, {"role": "option", "name": "National Higher Diploma"}, {"role": "option", "name": "National Masters Diploma"}, {"role": "option", "name": "National N Certificate"}, {"role": "option", "name": "National N Diploma"}, {"role": "option", "name": "NLRD Exclusion"}, {"role": "option", "name": "NO ANSWER"}, {"role": "option", "name": "Non-professional administration"}, {"role": "option", "name": "NON-RESPONSIVE"}, {"role": "option", "name": "Occupational Certificate"}, {"role": "option", "name": "Open Days"}, {"role": "option", "name": "PG B Deg (phasing out) eg B ed"}, {"role": "option", "name": "Post Dip Diploma (phasing out)"}, {"role": "option", "name": "Post-basic Diploma"}, {"role": "option", "name": "Post-doctoral Degree"}, {"role": "option", "name": "Postgraduate diploma"}, {"role": "option", "name": "Pretoria Campus"}, {"role": "option", "name": "Qual at Nat Sen Cert level"}, {"role": "option", "name": "Re-Enrolled"}, {"role": "option", "name": "Re-instated"}, {"role": "option", "name": "Referrals"}, {"role": "option", "name": "Registrar"}, {"role": "option", "name": "RPL"}, {"role": "option", "name": "Sales Agent"}, {"role": "option", "name": "Sales Team Manager"}, {"role": "option", "name": "Schl below SenC: not full qual"}, {"role": "option", "name": "Schools Direct"}, {"role": "option", "name": "SEMI"}, {"role": "option", "name": "Send to IVA"}, {"role": "option", "name": "Senior Certificate"}, {"role": "option", "name": "Service"}, {"role": "option", "name": "Sinethemba"}, {"role": "option", "name": "Specialised/Support professional"}, {"role": "option", "name": "SPOKE TO LEAD"}, {"role": "option", "name": "Staff Discount"}, {"role": "option", "name": "Stellenbosch Campus"}, {"role": "option", "name": "Student BA: <PERSON> Jockey"}, {"role": "option", "name": "Student BA: Expo Agent"}, {"role": "option", "name": "Student BA: Moving Billboard"}, {"role": "option", "name": "Student BA: <PERSON><PERSON>udd<PERSON>"}, {"role": "option", "name": "Super Admin"}, {"role": "option", "name": "Technical"}, {"role": "option", "name": "Thomas More Exchange Student"}, {"role": "option", "name": "Transfer"}, {"role": "option", "name": "Unallocated"}, {"role": "option", "name": "UNASSIGNED"}, {"role": "option", "name": "Unknown"}, {"role": "option", "name": "Unsubscribed"}, {"role": "option", "name": "Unsubscribed"}, {"role": "option", "name": "Value Add Interest Leads"}, {"role": "option", "name": "Vehicle Owner"}, {"role": "option", "name": "<PERSON><PERSON>"}], "value": "None"}, {"role": "text", "name": "Student Status"}, {"role": "combobox", "name": "Student Status", "haspopup": "menu", "children": [{"role": "option", "name": "All", "selected": true}, {"role": "option", "name": "Enrolled"}, {"role": "option", "name": "Deferred"}, {"role": "option", "name": "Discontinued"}, {"role": "option", "name": "Completed"}, {"role": "option", "name": "De-Enrolled"}, {"role": "option", "name": "Expired"}], "value": "All"}, {"role": "text", "name": "Year"}, {"role": "combobox", "name": "Year", "haspopup": "menu", "children": [{"role": "option", "name": "All", "selected": true}, {"role": "option", "name": "2016"}, {"role": "option", "name": "2017"}, {"role": "option", "name": "2018"}, {"role": "option", "name": "2019"}, {"role": "option", "name": "2020"}, {"role": "option", "name": "2021"}, {"role": "option", "name": "2022"}, {"role": "option", "name": "2023"}, {"role": "option", "name": "2024"}, {"role": "option", "name": "2025"}], "value": "All"}, {"role": "textbox", "name": "Search..."}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "textbox", "name": "", "multiline": true, "children": [{"role": "text", "name": "1"}], "value": "1"}, {"role": "text", "name": " of "}, {"role": "text", "name": "346"}, {"role": "text", "name": " Pages"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "6 920 Results"}, {"role": "button", "name": "20 "}, {"role": "text", "name": " per page"}, {"role": "text", "name": " FIRST NAME"}, {"role": "text", "name": "LAST NAME"}, {"role": "text", "name": "ID NUMBER"}, {"role": "text", "name": "AGE"}, {"role": "text", "name": "EMAIL ADDRESS"}, {"role": "text", "name": "CELLPHONE NUMBER"}, {"role": "text", "name": "<PERSON><PERSON>"}, {"role": "text", "name": "Nkole"}, {"role": "text", "name": "OP1290952"}, {"role": "text", "name": "23"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "+27814438157"}, {"role": "text", "name": "A-isha"}, {"role": "text", "name": "<PERSON>"}, {"role": "text", "name": "0206170088085"}, {"role": "text", "name": "23"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "0734370258"}, {"role": "text", "name": "<PERSON><PERSON><PERSON>"}, {"role": "text", "name": "<PERSON><PERSON><PERSON>"}, {"role": "text", "name": "0211110808089"}, {"role": "text", "name": "22"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "+27820769573"}, {"role": "text", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"role": "text", "name": "<PERSON>"}, {"role": "text", "name": "9204140246082"}, {"role": "text", "name": "33"}, {"role": "text", "name": "a<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"role": "text", "name": "27738784535"}, {"role": "text", "name": "Aalia"}, {"role": "text", "name": "<PERSON><PERSON>"}, {"role": "text", "name": "0311040356082"}, {"role": "text", "name": "21"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "+27827971920"}, {"role": "text", "name": "<PERSON><PERSON><PERSON>"}, {"role": "text", "name": "<PERSON><PERSON>"}, {"role": "text", "name": "0607300116086"}, {"role": "text", "name": "18"}, {"role": "text", "name": "a<PERSON><PERSON><EMAIL>"}, {"role": "text", "name": "+27832115290"}, {"role": "text", "name": "<PERSON>"}, {"role": "text", "name": "<PERSON>"}, {"role": "text", "name": "9211255082087"}, {"role": "text", "name": "32"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "0741020314"}, {"role": "text", "name": "<PERSON>"}, {"role": "text", "name": "Biggar"}, {"role": "text", "name": "9502225072087"}, {"role": "text", "name": "30"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "0727838868"}, {"role": "text", "name": "<PERSON><PERSON><PERSON>"}, {"role": "text", "name": "<PERSON>"}, {"role": "text", "name": "Z3352868"}, {"role": "text", "name": "a<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"role": "text", "name": "+971525860479"}, {"role": "text", "name": "Abadina"}, {"role": "text", "name": "<PERSON><PERSON><PERSON>"}, {"role": "text", "name": "OB0050299"}, {"role": "text", "name": "32"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "27727400913"}, {"role": "text", "name": "Abago"}, {"role": "text", "name": "<PERSON><PERSON><PERSON>"}, {"role": "text", "name": "0604195242085"}, {"role": "text", "name": "19"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "27744256969"}, {"role": "text", "name": "<PERSON><PERSON><PERSON>"}, {"role": "text", "name": "Brits"}, {"role": "text", "name": "0601020792086"}, {"role": "text", "name": "19"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "+27845450489"}, {"role": "text", "name": "Abbigail"}, {"role": "text", "name": "Chetty"}, {"role": "text", "name": "0209300743082"}, {"role": "text", "name": "22"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "+27845658979"}, {"role": "text", "name": "Abby"}, {"role": "text", "name": "<PERSON>"}, {"role": "text", "name": "0308060112080"}, {"role": "text", "name": "21"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "+27722842710"}, {"role": "text", "name": "Abby"}, {"role": "text", "name": "<PERSON>"}, {"role": "text", "name": "0307080285082"}, {"role": "text", "name": "22"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "+27609851217"}, {"role": "text", "name": "Abby-<PERSON>"}, {"role": "text", "name": "<PERSON><PERSON>"}, {"role": "text", "name": "0005310123087"}, {"role": "text", "name": "25"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "0833024081"}, {"role": "text", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"role": "text", "name": "Rage"}, {"role": "text", "name": "P01246539"}, {"role": "text", "name": "20"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "252610997851"}, {"role": "text", "name": "<PERSON>"}, {"role": "text", "name": "<PERSON><PERSON><PERSON>"}, {"role": "text", "name": "0103206418081"}, {"role": "text", "name": "24"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "0815065425"}, {"role": "text", "name": "<PERSON><PERSON>"}, {"role": "text", "name": "<PERSON><PERSON><PERSON>"}, {"role": "text", "name": "A11549045"}, {"role": "text", "name": "21"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "+2349041809416"}, {"role": "text", "name": "<PERSON><PERSON>"}, {"role": "text", "name": "<PERSON><PERSON><PERSON>"}, {"role": "text", "name": "A09706725"}, {"role": "text", "name": "<EMAIL>"}, {"role": "text", "name": "0505114420"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "textbox", "name": "", "multiline": true, "children": [{"role": "text", "name": "1"}], "value": "1"}, {"role": "text", "name": " of "}, {"role": "text", "name": "346"}, {"role": "text", "name": " Pages"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "6 920 Results"}, {"role": "button", "name": "20 "}, {"role": "text", "name": " per page"}, {"role": "button", "name": "NEW INDIVIDUAL"}, {"role": "button", "name": ""}]}, "metadata": {"name": "dashboard", "description": "Main dashboard after login", "category": "baseline", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "viewport": {"width": 1920, "height": 1080}}}