import { chromium } from '@playwright/test';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

/**
 * Simple ARIA snapshot generator script
 */
async function generateAriaSnapshots() {
    console.log('🚀 Starting ARIA snapshot generation...');

    const browser = await chromium.launch({
        headless: false,
        slowMo: 1000
    });

    const page = await browser.newPage({
        viewport: { width: 1920, height: 1080 }
    });

    try {
        // Login to INCONNECT
        console.log('🔐 Logging into INCONNECT...');
        await page.goto('https://connect.inscape.co.za/UserManagement/login/');
        await page.waitForLoadState('networkidle');

        // Debug: Check what's on the page
        console.log('🔍 Inspecting login page...');
        const title = await page.title();
        console.log(`Page title: ${title}`);

        // Try to find login form elements (updated with correct selectors)
        const emailSelectors = [
            '#c1',  // Actual username field
            'input[name="c1"]',
            'input[placeholder="Username"]',
            'input[name="email"]',
            'input[type="email"]',
            '#email',
            'input[placeholder*="email" i]',
            'input[placeholder*="Email" i]',
            'input[id*="email" i]',
            'input[name*="email" i]'
        ];

        const passwordSelectors = [
            '#c2',  // Actual password field
            'input[name="c2"]',
            'input[placeholder="Password"]',
            'input[name="password"]',
            'input[type="password"]',
            '#password',
            'input[placeholder*="password" i]',
            'input[placeholder*="Password" i]',
            'input[id*="password" i]',
            'input[name*="password" i]'
        ];

        let emailField = null;
        let passwordField = null;

        // Find email field
        for (const selector of emailSelectors) {
            try {
                emailField = await page.$(selector);
                if (emailField) {
                    console.log(`✅ Found email field with selector: ${selector}`);
                    break;
                }
            } catch (error) {
                // Continue to next selector
            }
        }

        // Find password field
        for (const selector of passwordSelectors) {
            try {
                passwordField = await page.$(selector);
                if (passwordField) {
                    console.log(`✅ Found password field with selector: ${selector}`);
                    break;
                }
            } catch (error) {
                // Continue to next selector
            }
        }

        if (!emailField || !passwordField) {
            console.log('⚠️ Could not find login form fields. Available inputs:');
            const inputs = await page.$$eval('input', inputs =>
                inputs.map(input => ({
                    type: input.type,
                    name: input.name,
                    id: input.id,
                    placeholder: input.placeholder,
                    className: input.className
                }))
            );
            console.log(JSON.stringify(inputs, null, 2));

            // Try generic approach
            const allInputs = await page.$$('input');
            if (allInputs.length >= 2) {
                emailField = allInputs[0];
                passwordField = allInputs[1];
                console.log('🔄 Using first two input fields as fallback');
            }
        }

        if (emailField && passwordField) {
            // Fill login form
            await emailField.fill('<EMAIL>');
            await passwordField.fill('Password1!');

            // Find and click submit button
            const submitSelectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                '.login-button',
                'button:has-text("Login")',
                'button:has-text("Sign In")',
                'form button',
                'button'
            ];

            let submitted = false;
            for (const selector of submitSelectors) {
                try {
                    await page.click(selector);
                    await page.waitForTimeout(2000);
                    submitted = true;
                    break;
                } catch (error) {
                    console.log(`Submit selector ${selector} failed, trying next...`);
                }
            }

            if (!submitted) {
                // Try pressing Enter on password field
                await passwordField.press('Enter');
            }

            await page.waitForLoadState('networkidle');
        } else {
            throw new Error('Could not find login form fields');
        }

        // Verify login success
        const currentUrl = page.url();
        if (currentUrl.includes('login')) {
            throw new Error('Authentication failed - still on login page');
        }
        console.log('✅ Authentication successful');

        // Ensure directories exist
        const campaignsDir = join(process.cwd(), 'snapshots', 'aria', 'campaigns');
        const baselineDir = join(process.cwd(), 'snapshots', 'aria', 'baseline');

        if (!existsSync(campaignsDir)) {
            mkdirSync(campaignsDir, { recursive: true });
        }
        if (!existsSync(baselineDir)) {
            mkdirSync(baselineDir, { recursive: true });
        }

        // Generate baseline snapshots
        console.log('📸 Generating baseline snapshots...');

        // Dashboard snapshot
        const dashboardSnapshot = await page.accessibility.snapshot();
        const dashboardData = {
            timestamp: new Date().toISOString(),
            url: page.url(),
            snapshot: dashboardSnapshot,
            metadata: {
                name: 'dashboard',
                description: 'Main dashboard after login',
                category: 'baseline',
                userAgent: await page.evaluate(() => navigator.userAgent),
                viewport: page.viewportSize() || { width: 1920, height: 1080 }
            }
        };
        writeFileSync(join(baselineDir, 'dashboard.json'), JSON.stringify(dashboardData, null, 2));
        console.log('✅ Dashboard snapshot saved');

        // Navigate to campaigns page
        console.log('📸 Generating campaign snapshots...');

        // Try different selectors for campaigns navigation
        const campaignSelectors = [
            'a[href*="campaign"]',
            '.campaigns-link',
            '[data-testid="campaigns"]',
            'a:has-text("Campaign")',
            'a:has-text("Campaigns")'
        ];

        let campaignNavigated = false;
        for (const selector of campaignSelectors) {
            try {
                await page.click(selector);
                await page.waitForTimeout(2000);
                campaignNavigated = true;
                break;
            } catch (error) {
                console.log(`Selector ${selector} not found, trying next...`);
            }
        }

        if (!campaignNavigated) {
            console.log('⚠️ Could not navigate to campaigns page, capturing current page snapshot');
        }

        // Try to capture lead details snapshot
        console.log('📸 Attempting to capture lead details snapshot...');
        try {
            // Try to navigate to a lead or open lead details
            const leadSelectors = [
                'tbody.jdtable-body tr.jdtable-row:first-child',
                '.lead-row:first-child',
                'tr[data-lead-id]:first-child',
                'tbody tr:first-child'
            ];

            let leadClicked = false;
            for (const selector of leadSelectors) {
                try {
                    await page.click(selector);
                    await page.waitForTimeout(3000);
                    leadClicked = true;
                    console.log(`✅ Clicked lead with selector: ${selector}`);
                    break;
                } catch (error) {
                    console.log(`Lead selector ${selector} not found, trying next...`);
                }
            }

            if (leadClicked) {
                // Try to capture the lead details container
                const leadDetailsSelectors = [
                    '#lead-details',
                    '.lead-details',
                    '[data-testid="lead-details"]'
                ];

                let leadDetailsSnapshot;
                let usedLeadSelector = '';

                for (const selector of leadDetailsSelectors) {
                    try {
                        const element = await page.$(selector);
                        if (element) {
                            leadDetailsSnapshot = await page.accessibility.snapshot({ root: element });
                            usedLeadSelector = selector;
                            break;
                        }
                    } catch (error) {
                        // Continue to next selector
                    }
                }

                if (leadDetailsSnapshot) {
                    const leadDetailsData = {
                        timestamp: new Date().toISOString(),
                        url: page.url(),
                        selector: usedLeadSelector,
                        snapshot: leadDetailsSnapshot,
                        metadata: {
                            name: 'lead-details',
                            description: 'Lead details container with tabs and content',
                            category: 'campaigns',
                            userAgent: await page.evaluate(() => navigator.userAgent),
                            viewport: page.viewportSize() || { width: 1920, height: 1080 }
                        }
                    };
                    writeFileSync(join(campaignsDir, 'lead-details.json'), JSON.stringify(leadDetailsData, null, 2));
                    console.log('✅ Lead details snapshot saved');
                } else {
                    console.log('⚠️ Could not find lead details container');
                }
            } else {
                console.log('⚠️ Could not click on any lead to open details');
            }
        } catch (error) {
            console.log('⚠️ Error capturing lead details:', error);
        }

        // Campaign tiles snapshot
        const campaignTilesSelectors = [
            '.campaign-tiles-container',
            '.campaigns-container',
            '[data-testid="campaign-tiles"]',
            '.campaign-list',
            '.campaigns'
        ];

        let campaignTilesSnapshot;
        let usedSelector = '';

        for (const selector of campaignTilesSelectors) {
            try {
                const element = await page.$(selector);
                if (element) {
                    campaignTilesSnapshot = await page.accessibility.snapshot({ root: element });
                    usedSelector = selector;
                    break;
                }
            } catch (error) {
                // Continue to next selector
            }
        }

        if (!campaignTilesSnapshot) {
            // Fallback to full page snapshot
            campaignTilesSnapshot = await page.accessibility.snapshot();
            usedSelector = 'body';
        }

        const campaignTilesData = {
            timestamp: new Date().toISOString(),
            url: page.url(),
            selector: usedSelector,
            snapshot: campaignTilesSnapshot,
            metadata: {
                name: 'campaign-tiles',
                description: 'Campaign tiles container on campaigns page',
                category: 'campaigns',
                userAgent: await page.evaluate(() => navigator.userAgent),
                viewport: page.viewportSize() || { width: 1920, height: 1080 }
            }
        };
        writeFileSync(join(campaignsDir, 'campaign-tiles.json'), JSON.stringify(campaignTilesData, null, 2));
        console.log('✅ Campaign tiles snapshot saved');

        console.log('🎉 ARIA snapshots generated successfully!');
        console.log('📁 Snapshots saved to:');
        console.log(`   - ${join(baselineDir, 'dashboard.json')}`);
        console.log(`   - ${join(campaignsDir, 'campaign-tiles.json')}`);

    } catch (error) {
        console.error('❌ Error generating snapshots:', error);
        throw error;
    } finally {
        await browser.close();
    }
}

// Run the generator
generateAriaSnapshots().catch(console.error);